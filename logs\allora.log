2025-07-16 19:08:20 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-16 19:08:20 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-16 19:08:20 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-16 19:08:20 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-16 19:08:20 - startup - INFO - ============================================================
2025-07-16 19:08:20 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-16 19:08:20 - startup - INFO - ============================================================
2025-07-16 19:08:20 - startup - INFO - 📅 Startup Time: 2025-07-16T19:08:20.299690
2025-07-16 19:08:20 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-16 19:08:20 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project
2025-07-16 19:08:20 - startup - INFO - 🔧 Log Level: INFO
2025-07-16 19:08:20 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-16 19:08:20 - startup - INFO - ============================================================
2025-07-16 19:08:20 - allora - INFO - 🔧 Using configuration: DevelopmentConfig
2025-07-16 19:08:20 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: development)
2025-07-16 19:08:20 - allora - INFO - ✅ Centralized configuration applied
2025-07-16 19:08:20 - engineio.server - INFO - Server initialized for threading.
2025-07-16 19:08:20 - flask_socketio_manager - INFO - ✅ Redis connected for Flask-SocketIO pub/sub
2025-07-16 19:08:20 - flask_socketio_manager - INFO - Flask-SocketIO manager initialized successfully
2025-07-16 19:08:20 - allora - INFO - ✅ Flask-SocketIO initialized successfully
2025-07-16 19:08:20 - allora - INFO - ✅ CORS configured with origins: ['http://localhost:3000', 'http://localhost:3001']
2025-07-16 19:08:20 - allora - INFO - ✅ JWT configuration applied from centralized config
2025-07-16 19:08:20 - allora - INFO - ✅ SECRET_KEY configuration applied from centralized config
2025-07-16 19:08:20 - allora - INFO - ✅ Database configuration applied from centralized config
2025-07-16 19:08:20 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-16 19:08:20 - allora - INFO - 🔧 Pool size: 10
2025-07-16 19:08:20 - allora - INFO - 🔧 Max overflow: 20
2025-07-16 19:08:20 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-16 19:08:20 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-16 19:08:20 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-16 19:08:20 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.020s]
2025-07-16 19:08:20 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-16 19:08:20 - allora - INFO - Search API blueprint registered successfully
2025-07-16 19:08:20 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-16 19:08:21 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-16 19:08:21 - allora - INFO - Behavior tracker initialized successfully
2025-07-16 19:08:21 - order_fulfillment.fulfillment_rules - INFO - Loaded 11 default fulfillment rules
2025-07-16 19:08:21 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-16 19:08:21 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-16 19:08:21 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-16 19:08:21 - allora - INFO - RMA API blueprint registered successfully
2025-07-16 19:08:21 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-16 19:08:21 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-16 19:08:21 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-16 19:08:21 - allora - INFO - SocketIO API routes registered successfully
2025-07-16 19:08:21 - tracking_system - INFO - Real-time tracking system started
2025-07-16 19:08:21 - allora - INFO - Real-time tracking system initialized successfully
2025-07-16 19:08:21 - notification_service - INFO - Notification delivery service started
2025-07-16 19:08:21 - allora - INFO - Notification service initialized successfully
2025-07-16 19:08:21 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'c:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-16 19:08:21 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-16 19:08:21 - allora - INFO - Recommendation system initialized successfully
2025-07-16 19:16:40 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-16 19:16:40 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-16 19:16:40 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-16 19:16:40 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-16 19:16:40 - startup - INFO - ============================================================
2025-07-16 19:16:40 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-16 19:16:40 - startup - INFO - ============================================================
2025-07-16 19:16:40 - startup - INFO - 📅 Startup Time: 2025-07-16T19:16:40.178716
2025-07-16 19:16:40 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-16 19:16:40 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project
2025-07-16 19:16:40 - startup - INFO - 🔧 Log Level: INFO
2025-07-16 19:16:40 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-16 19:16:40 - startup - INFO - ============================================================
2025-07-16 19:16:40 - allora - INFO - 🔧 Using configuration: DevelopmentConfig
2025-07-16 19:16:40 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: development)
2025-07-16 19:16:40 - allora - INFO - ✅ Centralized configuration applied
2025-07-16 19:16:40 - engineio.server - INFO - Server initialized for threading.
2025-07-16 19:16:40 - flask_socketio_manager - INFO - ✅ Redis connected for Flask-SocketIO pub/sub
2025-07-16 19:16:40 - flask_socketio_manager - INFO - Flask-SocketIO manager initialized successfully
2025-07-16 19:16:40 - allora - INFO - ✅ Flask-SocketIO initialized successfully
2025-07-16 19:16:40 - allora - INFO - ✅ CORS configured with origins: ['http://localhost:3000', 'http://localhost:3001']
2025-07-16 19:16:40 - allora - INFO - ✅ JWT configuration applied from centralized config
2025-07-16 19:16:40 - allora - INFO - ✅ SECRET_KEY configuration applied from centralized config
2025-07-16 19:16:40 - allora - INFO - ✅ Database configuration applied from centralized config
2025-07-16 19:16:40 - allora - INFO - ✅ Database configured: localhost:3306/allora_db
2025-07-16 19:16:40 - allora - INFO - 🔧 Pool size: 10
2025-07-16 19:16:40 - allora - INFO - 🔧 Max overflow: 20
2025-07-16 19:16:40 - allora - INFO - 🔧 Pool timeout: 30s
2025-07-16 19:16:40 - redis_config - INFO - ✅ Connected to Redis at localhost:6379
2025-07-16 19:16:40 - allora - INFO - ✅ Redis connected: localhost:6379
2025-07-16 19:16:40 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.024s]
2025-07-16 19:16:40 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-16 19:16:40 - allora - INFO - Search API blueprint registered successfully
2025-07-16 19:16:40 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-16 19:16:40 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-16 19:16:40 - allora - INFO - Behavior tracker initialized successfully
2025-07-16 19:16:41 - order_fulfillment.fulfillment_rules - INFO - Loaded 11 default fulfillment rules
2025-07-16 19:16:41 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-16 19:16:41 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-16 19:16:41 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-16 19:16:41 - allora - INFO - RMA API blueprint registered successfully
2025-07-16 19:16:41 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-16 19:16:41 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-16 19:16:41 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-16 19:16:41 - allora - INFO - SocketIO API routes registered successfully
2025-07-16 19:16:41 - tracking_system - INFO - Real-time tracking system started
2025-07-16 19:16:41 - allora - INFO - Real-time tracking system initialized successfully
2025-07-16 19:16:41 - notification_service - INFO - Notification delivery service started
2025-07-16 19:16:41 - allora - INFO - Notification service initialized successfully
2025-07-16 19:16:41 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'c:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-16 19:16:41 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-16 19:16:41 - allora - INFO - Recommendation system initialized successfully
