#!/usr/bin/env python3
"""
Comprehensive Database Seeding Script for Allora Sustainable E-commerce Platform
================================================================================

This script seeds the entire database with realistic data for a sustainable e-commerce platform.
It creates:
- 100+ sustainable products across 10 categories
- 50+ users with realistic profiles
- 20+ sellers with sustainable business profiles
- 200+ orders with realistic purchase patterns
- 500+ product reviews and ratings
- Community posts, comments, and engagement data
- Complete supporting data (categories, coupons, shipping, etc.)

Usage:
    python seed_database.py

Requirements:
    - Flask app context
    - Database connection configured
    - All models imported from app.py
"""

import os
import sys
import random
import json
from datetime import datetime, timedelta
from decimal import Decimal
from faker import Faker
import hashlib

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Initialize Faker for generating realistic data
fake = Faker()

# Product categories and their characteristics
SUSTAINABLE_CATEGORIES = {
    'Home & Living': {
        'description': 'Essential home products for modern living',
        'keywords': ['durable', 'quality', 'functional', 'stylish', 'practical'],
        'price_range': (15, 200),
        'sustainability_score': (8, 10)
    },
    'Fashion & Apparel': {
        'description': 'Trendy clothing and accessories for all occasions',
        'keywords': ['comfortable', 'stylish', 'quality', 'trendy', 'versatile'],
        'price_range': (25, 300),
        'sustainability_score': (7, 10)
    },
    'Electronics & Gadgets': {
        'description': 'Latest technology and electronic devices',
        'keywords': ['innovative', 'efficient', 'modern', 'smart', 'reliable'],
        'price_range': (50, 800),
        'sustainability_score': (6, 9)
    },
    'Beauty & Personal Care': {
        'description': 'Premium beauty and personal care products',
        'keywords': ['premium', 'gentle', 'effective', 'luxurious', 'quality'],
        'price_range': (10, 150),
        'sustainability_score': (8, 10)
    },
    'Sports & Fitness': {
        'description': 'Professional sports and fitness equipment',
        'keywords': ['professional', 'durable', 'performance', 'quality', 'reliable'],
        'price_range': (20, 400),
        'sustainability_score': (7, 9)
    },
    'Kitchen & Dining': {
        'description': 'Essential kitchen tools and dining accessories',
        'keywords': ['practical', 'durable', 'functional', 'convenient', 'quality'],
        'price_range': (5, 100),
        'sustainability_score': (9, 10)
    },
    'Food & Beverages': {
        'description': 'Premium food products and beverages',
        'keywords': ['fresh', 'premium', 'quality', 'nutritious', 'delicious'],
        'price_range': (8, 80),
        'sustainability_score': (8, 10)
    },
    'Baby & Kids': {
        'description': 'Safe and quality products for babies and children',
        'keywords': ['safe', 'quality', 'comfortable', 'durable', 'reliable'],
        'price_range': (12, 200),
        'sustainability_score': (9, 10)
    },
    'Garden & Outdoor': {
        'description': 'Gardening tools and outdoor equipment',
        'keywords': ['durable', 'practical', 'reliable', 'efficient', 'quality'],
        'price_range': (10, 150),
        'sustainability_score': (8, 10)
    },
    'Tools & Hardware': {
        'description': 'Professional tools and hardware solutions',
        'keywords': ['professional', 'durable', 'reliable', 'efficient', 'quality'],
        'price_range': (100, 2000),
        'sustainability_score': (9, 10)
    }
}

    # Product names and descriptions
SUSTAINABLE_PRODUCTS = {
    'Home & Living': [
        ('Premium Cutting Board Set', 'Set of 3 professional cutting boards with antimicrobial properties'),
        ('Crystal Clear Water Bottles', 'Beautiful water bottles with leak-proof design'),
        ('Luxury Bed Sheet Collection', 'Ultra-soft bed sheets with premium comfort'),
        ('Professional Yoga Mat', 'Non-slip yoga mat with excellent grip and durability'),
        ('Gourmet Kitchen Utensil Set', 'Complete kitchen utensil set for culinary enthusiasts'),
        ('Modular Storage Container System', 'Stackable storage containers with airtight seals'),
        ('Artisan Woven Basket Collection', 'Stylish storage baskets for home organization'),
        ('Premium Cleaning Sponge Pack', 'High-quality cleaning sponges for all surfaces'),
        ('Decorative LED String Lights', 'Beautiful ambient lighting for any space'),
        ('Rustic Picture Frame Collection', 'Handcrafted picture frames with vintage charm'),
        ('Cozy Throw Blanket', 'Luxuriously soft throw blanket for ultimate comfort'),
        ('Hotel Quality Towel Set', 'Ultra-absorbent towels with spa-like softness'),
        ('Modern Metal Wall Art', 'Contemporary wall art for stylish home decor'),
        ('Artisan Scented Candles', 'Hand-poured candles with long-lasting fragrance'),
        ('Elegant Coaster Set', 'Sophisticated coasters for entertaining guests')
    ],
    'Fashion & Apparel': [
        ('Classic Cotton T-Shirt', 'Comfortable everyday t-shirt with perfect fit'),
        ('Premium Denim Jeans', 'Durable jeans with modern styling and comfort'),
        ('Merino Wool Sweater', 'Cozy sweater with luxurious feel and warmth'),
        ('All-Weather Jacket', 'Versatile jacket for any weather condition'),
        ('Elegant Linen Dress', 'Sophisticated dress perfect for any occasion'),
        ('Performance Athletic Socks', 'Moisture-wicking socks for active lifestyle'),
        ('Comfort Modal Underwear', 'Ultra-soft underwear with all-day comfort'),
        ('Designer Leather Handbag', 'Stylish handbag with premium craftsmanship'),
        ('Luxury Pajama Set', 'Comfortable sleepwear for restful nights'),
        ('Cashmere Blend Scarf', 'Warm and stylish scarf for cold weather'),
        ('Canvas Casual Sneakers', 'Comfortable sneakers for everyday wear'),
        ('Silk Blouse Collection', 'Elegant blouse with timeless appeal'),
        ('Athletic Performance Wear', 'High-performance activewear for fitness enthusiasts'),
        ('Cotton Comfort Hoodie', 'Cozy hoodie for casual comfort'),
        ('Professional Dress Pants', 'Tailored trousers for business and formal wear')
    ],
    'Electronics & Gadgets': [
        ('Portable Power Bank', 'High-capacity portable charger with fast charging technology'),
        ('Ultra HD LED Monitor', '24-inch monitor with crystal clear display and energy efficiency'),
        ('Wireless Ergonomic Keyboard', 'Comfortable keyboard with premium build quality'),
        ('Waterproof Bluetooth Speaker', 'Premium speaker with exceptional sound quality'),
        ('Protective Phone Case', 'Durable phone case with shock absorption technology'),
        ('Emergency Hand Crank Radio', 'Reliable radio for emergency situations and outdoor use'),
        ('Smart Programmable Thermostat', 'Intelligent thermostat with energy-saving features'),
        ('LED Garden Light Set', 'Decorative outdoor lighting with automatic sensors'),
        ('Adjustable Laptop Stand', 'Ergonomic laptop stand for comfortable computing'),
        ('Premium Tablet Stand', 'Sleek and sturdy stand for tablets and e-readers'),
        ('Wireless Security Camera', 'HD security camera with night vision capabilities'),
        ('Smart LED Light Bulbs', 'Color-changing smart bulbs with app control'),
        ('Tempered Glass Screen Protector', 'Crystal clear protection for mobile devices'),
        ('Car Phone Charger', 'Fast charging car adapter with multiple ports'),
        ('Cable Management Organizer', 'Keep your cables neat and organized')
    ],
    'Beauty & Personal Care': [
        ('Argan Oil Hair Serum', 'Nourishing hair serum for shine and repair'),
        ('Charcoal Detox Face Mask', 'Deep cleansing face mask for all skin types'),
        ('Moisturizing Lip Balm Set', 'Hydrating lip balms in various natural flavors'),
        ('Aluminum-Free Deodorant', 'Long-lasting deodorant with natural ingredients'),
        ('Anti-Aging Facial Oil', 'Luxurious facial oil with vitamin-rich formula'),
        ('Professional Makeup Brush Set', 'High-quality brushes for flawless makeup application'),
        ('Exfoliating Body Scrub', 'Gentle body scrub for smooth, soft skin'),
        ('Solid Shampoo Bar', 'Concentrated shampoo bar for healthy hair'),
        ('Chip-Resistant Nail Polish', 'Long-lasting nail polish in trendy colors'),
        ('Gentle Face Cleanser', 'Mild cleanser suitable for sensitive skin'),
        ('Hydrating Body Moisturizer', 'Rich moisturizer for all-day skin hydration'),
        ('Volume Enhancing Mascara', 'Professional mascara for dramatic lashes'),
        ('Broad Spectrum Sunscreen', 'Daily protection with SPF 30 formula'),
        ('Essential Oil Starter Kit', 'Collection of popular essential oils for wellness'),
        ('Reusable Makeup Remover Pads', 'Soft and gentle pads for makeup removal')
    ],
    'Sports & Fitness': [
        ('Professional Yoga Mat', 'Non-slip yoga mat with superior grip and cushioning'),
        ('Quick-Dry Gym Towel', 'Ultra-absorbent workout towel with antimicrobial treatment'),
        ('Insulated Water Bottle', 'Double-wall insulated bottle keeps drinks cold for 24 hours'),
        ('Adjustable Dumbbell Set', 'Space-saving dumbbells with quick weight adjustment'),
        ('Resistance Band Kit', 'Complete set of exercise bands with varying resistance levels'),
        ('Massage Ball Set', 'Self-massage tools for muscle recovery and relaxation'),
        ('Yoga Block Set', 'Supportive blocks for improved yoga practice'),
        ('Foam Roller Pro', 'High-density foam roller for deep tissue massage'),
        ('Performance Running Shoes', 'Lightweight running shoes with advanced cushioning'),
        ('Athletic Performance Socks', 'Moisture-wicking socks with arch support'),
        ('Exercise Mat Deluxe', 'Extra-thick exercise mat for floor workouts'),
        ('Speed Jump Rope', 'Professional jump rope with ball bearing system'),
        ('Sports Duffel Bag', 'Spacious gym bag with multiple compartments'),
        ('Fitness Activity Tracker', 'Advanced fitness tracker with heart rate monitoring'),
        ('Protein Shaker Bottle', 'Leak-proof shaker with mixing ball and measurement marks')
    ],
    'Kitchen & Dining': [
        ('Stainless Steel Straw Set', 'Reusable straws with cleaning brush and travel case'),
        ('Food Storage Wrap Set', 'Reusable food wraps for fresh food storage'),
        ('Glass Storage Jar Collection', 'Airtight glass jars for pantry organization'),
        ('Portable Cutlery Set', 'Travel-friendly utensil set with carrying case'),
        ('Mesh Produce Bags', 'Lightweight bags for fresh produce shopping'),
        ('Kitchen Trash Bags', 'Strong and reliable bags for kitchen waste'),
        ('Silicone Food Bags', 'Reusable food storage bags for meal prep'),
        ('Natural Bristle Toothbrush', 'Gentle toothbrush with ergonomic handle'),
        ('Stainless Steel Lunch Box', 'Durable lunch container with leak-proof design'),
        ('Natural Kitchen Sponges', 'Biodegradable sponges for eco-friendly cleaning'),
        ('Insulated Travel Mug', 'Double-wall travel mug with spill-proof lid'),
        ('Canvas Shopping Bags', 'Durable shopping bags with reinforced handles'),
        ('Borosilicate Water Bottles', 'Heat-resistant glass bottles with protective sleeve'),
        ('Pump Soap Dispensers', 'Elegant dispensers for liquid soap and lotion'),
        ('Biodegradable Phone Case', 'Protective case made from plant-based materials')
    ],
    'Food & Beverages': [
        ('Premium Quinoa Grain', 'High-quality quinoa with excellent nutritional value'),
        ('Artisan Coffee Beans', 'Carefully selected coffee beans with rich flavor profile'),
        ('Virgin Coconut Oil', 'Cold-pressed coconut oil for cooking and health'),
        ('Raw Wildflower Honey', 'Pure honey with natural sweetness and health benefits'),
        ('Superfood Chia Seeds', 'Nutrient-dense seeds perfect for healthy recipes'),
        ('Gourmet Sea Salt', 'Premium sea salt for culinary excellence'),
        ('Creamy Almond Butter', 'Smooth and delicious nut butter made from quality almonds'),
        ('Dark Chocolate Collection', 'Rich and indulgent chocolate with intense flavor'),
        ('Premium Green Tea', 'High-grade loose-leaf tea with delicate flavor'),
        ('Fresh Seasonal Vegetables', 'Crisp and nutritious vegetables from local farms'),
        ('Extra Virgin Olive Oil', 'Premium olive oil with exceptional taste and quality'),
        ('Plant-Based Protein Powder', 'Complete protein supplement for active lifestyles'),
        ('Dried Fruit Medley', 'Natural dried fruits without artificial additives'),
        ('Gourmet Spice Collection', 'Aromatic spices for culinary creativity'),
        ('Herbal Tea Variety Pack', 'Soothing herbal teas for relaxation and wellness')
    ],
    'Baby & Kids': [
        ('Soft Cotton Baby Clothes', 'Gentle and comfortable clothing for sensitive baby skin'),
        ('BPA-Free Baby Bottles', 'Safe feeding bottles with easy-grip design'),
        ('Silicone Teething Toys', 'Safe and soothing toys for teething babies'),
        ('Nutritious Baby Food Pouches', 'Convenient and healthy meals for growing babies'),
        ('Ultra-Soft Diapers', 'Comfortable diapers with superior absorption'),
        ('Educational Wooden Toys', 'Learning toys that stimulate creativity and development'),
        ('Gentle Baby Skincare Set', 'Mild and nourishing products for baby\'s delicate skin'),
        ('Adjustable High Chair', 'Ergonomic high chair that grows with your child'),
        ('Cozy Baby Blanket', 'Soft and warm heavyweight blanket for peaceful sleep'),
        ('Non-Toxic Art Supplies', 'Safe coloring materials for creative expression'),
        ('Plush Stuffed Animals', 'Cuddly companions made with love and care'),
        ('Ergonomic Baby Carrier', 'Comfortable carrier for bonding and convenience'),
        ('Classic Building Blocks', 'Timeless toys that encourage imagination and learning'),
        ('Tear-Free Baby Shampoo', 'Gentle cleansing formula for baby\'s hair and scalp'),
        ('Soft Silicone Pacifiers', 'Comforting pacifiers designed for baby\'s comfort')
    ],
    'Garden & Outdoor': [
        ('Premium Compost Fertilizer', 'Nutrient-rich fertilizer for healthy plant growth'),
        ('Professional Garden Tool Set', 'Durable gardening tools with ergonomic handles'),
        ('Decorative Planter Collection', 'Stylish planters for indoor and outdoor use'),
        ('Seed Starter Kit', 'Complete kit for starting your own garden from seeds'),
        ('Natural Pest Control Spray', 'Safe and effective pest control for gardens'),
        ('Biodegradable Plant Pots', 'Eco-friendly pots that naturally decompose in soil'),
        ('Premium Potting Soil Mix', 'High-quality soil blend for optimal plant growth'),
        ('LED Garden Light Set', 'Beautiful lighting to enhance your outdoor space'),
        ('Rainwater Collection Barrel', 'Efficient system for collecting and storing rainwater'),
        ('Natural Mulch Blend', 'Protective mulch to retain moisture and prevent weeds'),
        ('Gardening Reference Guide', 'Comprehensive guide for successful gardening'),
        ('Plant Identification Markers', 'Durable labels for organizing your garden'),
        ('Herb Garden Starter Kit', 'Everything needed to grow fresh culinary herbs'),
        ('Beneficial Insect Hotel', 'Habitat to encourage helpful garden insects'),
        ('Galvanized Watering Can', 'Classic watering can with comfortable grip')
    ],
    'Tools & Hardware': [
        ('Solar Panel Installation Kit', 'Complete system for residential solar power'),
        ('Portable Power Station', 'Compact generator for camping and emergencies'),
        ('Small Wind Turbine', 'Residential wind power generator for clean energy'),
        ('Solar Water Heating System', 'Efficient water heating using solar technology'),
        ('Lithium Battery Bank', 'High-capacity energy storage solution'),
        ('Ventilation Fan System', 'Automatic ventilation for improved air circulation'),
        ('Micro Hydro Generator', 'Small-scale water power for remote locations'),
        ('Charge Controller Unit', 'Smart controller for solar panel optimization'),
        ('Energy Monitoring System', 'Track and optimize your home energy usage'),
        ('Pool Heating System', 'Efficient heating solution for swimming pools'),
        ('Hybrid Power System', 'Combined renewable energy generation system'),
        ('Greenhouse Kit Pro', 'Complete greenhouse with climate control'),
        ('Heat Pump System', 'Energy-efficient heating and cooling solution'),
        ('Power Inverter System', 'Convert DC power to AC for home appliances'),
        ('Energy Consultation Service', 'Professional energy efficiency assessment')
    ]
}

# Sample seller businesses
SUSTAINABLE_SELLERS = [
    {
        'business_name': 'HomeStyle Living',
        'contact_person': 'Sarah Johnson',
        'description': 'Premium home products and lifestyle solutions for modern living',
        'sustainability_focus': 'Quality home products with sustainable manufacturing practices'
    },
    {
        'business_name': 'Urban Fashion Co.',
        'contact_person': 'Michael Chen',
        'description': 'Contemporary fashion brand offering stylish and comfortable clothing',
        'sustainability_focus': 'Ethical fashion with responsible sourcing and production'
    },
    {
        'business_name': 'TechForward Solutions',
        'contact_person': 'Lisa Rodriguez',
        'description': 'Innovative electronics and smart technology for everyday use',
        'sustainability_focus': 'Energy-efficient technology and responsible electronics'
    },
    {
        'business_name': 'Radiance Beauty',
        'contact_person': 'Emma Thompson',
        'description': 'Premium beauty products for natural radiance and self-care',
        'sustainability_focus': 'Clean beauty products with natural ingredients'
    },
    {
        'business_name': 'ActiveLife Sports',
        'contact_person': 'Jake Martinez',
        'description': 'Professional sports equipment and fitness gear for active lifestyles',
        'sustainability_focus': 'Durable sports equipment with responsible manufacturing'
    },
    {
        'business_name': 'Kitchen Essentials',
        'contact_person': 'Anna Williams',
        'description': 'Quality kitchen tools and dining accessories for culinary enthusiasts',
        'sustainability_focus': 'Long-lasting kitchen products with minimal environmental impact'
    },
    {
        'business_name': 'Harvest & Table',
        'contact_person': 'David Kumar',
        'description': 'Premium food products and gourmet ingredients for healthy living',
        'sustainability_focus': 'Responsibly sourced food products supporting local communities'
    },
    {
        'business_name': 'Little Ones Care',
        'contact_person': 'Maria Garcia',
        'description': 'Safe and quality products designed specifically for babies and children',
        'sustainability_focus': 'Non-toxic baby products with safety as top priority'
    },
    {
        'business_name': 'Garden & Bloom',
        'contact_person': 'Tom Anderson',
        'description': 'Gardening supplies and outdoor equipment for green thumb enthusiasts',
        'sustainability_focus': 'Supporting home gardening and outdoor sustainability'
    },
    {
        'business_name': 'PowerTech Solutions',
        'contact_person': 'Rachel Patel',
        'description': 'Advanced power systems and energy solutions for modern homes',
        'sustainability_focus': 'Clean energy technology and efficient power systems'
    }
]

# Popular hashtags for community posts
SUSTAINABILITY_HASHTAGS = [
    'lifestyle', 'quality', 'premium', 'modern', 'stylish',
    'comfortable', 'durable', 'innovative', 'practical',
    'healthy', 'natural', 'fresh', 'trending',
    'fashion', 'beauty', 'fitness', 'home',
    'technology', 'wellness', 'family', 'cooking',
    'garden', 'outdoor', 'shopping', 'reviews'
]

def hash_password(password):
    """Hash password using SHA-256"""
    return hashlib.sha256(password.encode()).hexdigest()

def random_date_between(start_date, end_date):
    """Generate random date between two dates"""
    time_between = end_date - start_date
    days_between = time_between.days
    random_days = random.randrange(days_between)
    return start_date + timedelta(days=random_days)

def random_datetime_between(start_date, end_date):
    """Generate random datetime between two dates"""
    time_between = end_date - start_date
    seconds_between = time_between.total_seconds()
    random_seconds = random.randrange(int(seconds_between))
    return start_date + timedelta(seconds=random_seconds)

class DatabaseSeeder:
    """Main class for seeding the database with sustainable e-commerce data"""
    
    def __init__(self):
        self.db = None
        self.models = {}
        self.created_data = {
            'categories': [],
            'users': [],
            'sellers': [],
            'admin_users': [],
            'products': [],
            'orders': [],
            'reviews': [],
            'posts': [],
            'hashtags': [],
            'shipping_zones': [],
            'shipping_methods': [],
            'coupons': [],
            'tax_rates': [],
            'payment_gateways': [],
            'oauth_providers': [],
            'shipping_carriers': []
        }
        
    def initialize_models(self):
        """Import all database models"""
        try:
            from app import (
                db, Category, Product, User, Seller, AdminUser, Order, OrderItem,
                ProductReview, ProductImage, ProductVariant, CartItem, Wishlist,
                CommunityPost, PostComment, PostLike, Hashtag, PostHashtag,
                UserAddress, PaymentMethod, ShippingZone, ShippingMethod,
                Coupon, CouponUsage, TaxRate, PaymentGateway, PaymentTransaction,
                Invoice, Refund, UserInteractionLog, UserBehaviorProfile,
                UserSession, Sales, PriceHistory, CommunityStats, OAuthProvider,
                UserOAuth, GuestSession, SavedCart, AbandonedCart, EmailNotification,
                RecentlyViewed, AvailabilityNotification, ProductComparison,
                SupportTicket, SupportMessage, SupportAttachment, Banner,
                ContentPage, NewsletterSubscription, SearchAnalytics,
                VisualSearchAnalytics, CookieConsent, SellerStore, SellerCommission,
                SellerPayout, ShippingCarrier, Shipment, TrackingEvent,
                FulfillmentRule, CarrierRate, RMARequest, RMAItem, RMATimeline,
                RMADocument, ReturnShipment, RMAApproval, RMARule, RMAConfiguration,
                RMAStats, InventoryLog, SalesChannel, ChannelInventory,
                InventorySyncLog, InventoryConflict, SyncQueue, SimpleProduct,
                SimpleUser, SimpleOrder, SimpleSeller, TestTable, ChatSession,
                ChatMessage, SearchClick, SearchConversion, CommunityInsight,
                CookieConsentHistory, CookieAuditLog, DataExportRequest
            )
            
            self.db = db
            self.models = {
                'Category': Category, 'Product': Product, 'User': User, 'Seller': Seller,
                'AdminUser': AdminUser, 'Order': Order, 'OrderItem': OrderItem,
                'ProductReview': ProductReview, 'ProductImage': ProductImage,
                'ProductVariant': ProductVariant, 'CartItem': CartItem, 'Wishlist': Wishlist,
                'CommunityPost': CommunityPost, 'PostComment': PostComment, 'PostLike': PostLike,
                'Hashtag': Hashtag, 'PostHashtag': PostHashtag, 'UserAddress': UserAddress,
                'PaymentMethod': PaymentMethod, 'ShippingZone': ShippingZone,
                'ShippingMethod': ShippingMethod, 'Coupon': Coupon, 'CouponUsage': CouponUsage,
                'TaxRate': TaxRate, 'PaymentGateway': PaymentGateway,
                'PaymentTransaction': PaymentTransaction, 'Invoice': Invoice, 'Refund': Refund,
                'UserInteractionLog': UserInteractionLog, 'UserBehaviorProfile': UserBehaviorProfile,
                'UserSession': UserSession, 'Sales': Sales, 'PriceHistory': PriceHistory,
                'CommunityStats': CommunityStats, 'OAuthProvider': OAuthProvider,
                'UserOAuth': UserOAuth, 'GuestSession': GuestSession, 'SavedCart': SavedCart,
                'AbandonedCart': AbandonedCart, 'EmailNotification': EmailNotification,
                'RecentlyViewed': RecentlyViewed, 'AvailabilityNotification': AvailabilityNotification,
                'ProductComparison': ProductComparison, 'SupportTicket': SupportTicket,
                'SupportMessage': SupportMessage, 'SupportAttachment': SupportAttachment,
                'Banner': Banner, 'ContentPage': ContentPage, 'NewsletterSubscription': NewsletterSubscription,
                'SearchAnalytics': SearchAnalytics, 'VisualSearchAnalytics': VisualSearchAnalytics,
                'CookieConsent': CookieConsent, 'SellerStore': SellerStore,
                'SellerCommission': SellerCommission, 'SellerPayout': SellerPayout,
                'ShippingCarrier': ShippingCarrier, 'Shipment': Shipment, 'TrackingEvent': TrackingEvent,
                'FulfillmentRule': FulfillmentRule, 'CarrierRate': CarrierRate,
                'RMARequest': RMARequest, 'RMAItem': RMAItem, 'RMATimeline': RMATimeline,
                'RMADocument': RMADocument, 'ReturnShipment': ReturnShipment,
                'RMAApproval': RMAApproval, 'RMARule': RMARule, 'RMAConfiguration': RMAConfiguration,
                'RMAStats': RMAStats, 'InventoryLog': InventoryLog, 'SalesChannel': SalesChannel,
                'ChannelInventory': ChannelInventory, 'InventorySyncLog': InventorySyncLog,
                'InventoryConflict': InventoryConflict, 'SyncQueue': SyncQueue,
                'SimpleProduct': SimpleProduct, 'SimpleUser': SimpleUser,
                'SimpleOrder': SimpleOrder, 'SimpleSeller': SimpleSeller, 'TestTable': TestTable,
                'ChatSession': ChatSession, 'ChatMessage': ChatMessage,
                'SearchClick': SearchClick, 'SearchConversion': SearchConversion,
                'CommunityInsight': CommunityInsight, 'CookieConsentHistory': CookieConsentHistory,
                'CookieAuditLog': CookieAuditLog, 'DataExportRequest': DataExportRequest
            }
            
            print("✅ Successfully imported all database models")
            return True

        except ImportError as e:
            print(f"❌ Failed to import models: {e}")
            return False

    def clear_existing_data(self):
        """Clear existing data from all tables"""
        print("🗑️  Clearing existing data...")

        try:
            # Clear in reverse dependency order to avoid foreign key constraints
            tables_to_clear = [
                'SearchConversion', 'SearchClick', 'ChatMessage', 'ChatSession',
                'DataExportRequest', 'CookieAuditLog', 'CookieConsentHistory',
                'CookieConsent', 'VisualSearchAnalytics', 'SearchAnalytics',
                'NewsletterSubscription', 'ContentPage', 'Banner', 'SupportAttachment',
                'SupportMessage', 'SupportTicket', 'ProductComparison',
                'AvailabilityNotification', 'RecentlyViewed', 'EmailNotification',
                'AbandonedCart', 'SavedCart', 'GuestSession', 'UserOAuth',
                'OAuthProvider', 'CommunityStats', 'PriceHistory', 'Sales',
                'UserSession', 'UserBehaviorProfile', 'UserInteractionLog',
                'Refund', 'Invoice', 'PaymentTransaction', 'PaymentGateway',
                'TaxRate', 'CouponUsage', 'Coupon', 'ShippingMethod', 'ShippingZone',
                'PaymentMethod', 'UserAddress', 'PostHashtag', 'Hashtag',
                'PostLike', 'PostComment', 'CommunityPost', 'Wishlist',
                'CartItem', 'ProductVariant', 'ProductImage', 'ProductReview',
                'OrderItem', 'Order', 'Product', 'Category', 'SellerPayout',
                'SellerCommission', 'SellerStore', 'Seller', 'AdminUser', 'User',
                'SyncQueue', 'InventoryConflict', 'InventorySyncLog', 'ChannelInventory',
                'SalesChannel', 'InventoryLog', 'RMAStats', 'RMAConfiguration',
                'RMARule', 'RMAApproval', 'ReturnShipment', 'RMADocument',
                'RMATimeline', 'RMAItem', 'RMARequest', 'CarrierRate',
                'FulfillmentRule', 'TrackingEvent', 'Shipment', 'ShippingCarrier'
            ]

            for table_name in tables_to_clear:
                if table_name in self.models:
                    try:
                        self.models[table_name].query.delete()
                        print(f"   Cleared {table_name}")
                    except Exception as e:
                        print(f"   Warning: Could not clear {table_name}: {e}")

            self.db.session.commit()
            print("✅ Successfully cleared existing data")

        except Exception as e:
            print(f"❌ Error clearing data: {e}")
            self.db.session.rollback()
            raise

    def create_categories(self):
        """Create product categories"""
        print("📂 Creating product categories...")

        for category_name, category_info in SUSTAINABLE_CATEGORIES.items():
            category = self.models['Category'](
                name=category_name,
                slug=category_name.lower().replace(' ', '-').replace('&', 'and'),
                description=category_info['description'],
                is_active=True,
                sort_order=len(self.created_data['categories']) + 1,
                meta_title=f"{category_name} - Sustainable Products",
                meta_description=category_info['description'][:160],
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )

            self.db.session.add(category)
            self.created_data['categories'].append(category)

        self.db.session.commit()
        print(f"✅ Created {len(self.created_data['categories'])} categories")

    def create_admin_users(self):
        """Create admin users"""
        print("👑 Creating admin users...")

        admin_users_data = [
            {
                'username': 'admin',
                'email': '<EMAIL>',
                'password': 'admin123',
                'first_name': 'System',
                'last_name': 'Administrator',
                'role': 'super_admin',
                'is_active': True,
                'can_manage_users': True  # Super admin can manage users
            },
            {
                'username': 'sustainability_manager',
                'email': '<EMAIL>',
                'password': 'sustain123',
                'first_name': 'Green',
                'last_name': 'Manager',
                'role': 'admin',
                'is_active': True,
                'can_manage_users': False
            },
            {
                'username': 'product_curator',
                'email': '<EMAIL>',
                'password': 'curator123',
                'first_name': 'Eco',
                'last_name': 'Curator',
                'role': 'manager',
                'is_active': True,
                'can_manage_users': False
            }
        ]

        for admin_data in admin_users_data:
            admin_user = self.models['AdminUser'](
                username=admin_data['username'],
                email=admin_data['email'],
                password=hash_password(admin_data['password']),
                first_name=admin_data['first_name'],
                last_name=admin_data['last_name'],
                role=admin_data['role'],
                is_active=admin_data['is_active'],
                can_manage_users=admin_data['can_manage_users'],
                created_at=datetime.utcnow()
            )

            self.db.session.add(admin_user)
            self.created_data['admin_users'].append(admin_user)

        self.db.session.commit()
        print(f"✅ Created {len(self.created_data['admin_users'])} admin users")

    def create_users(self):
        """Create regular users"""
        print("👥 Creating users...")

        # Create 50 diverse users with various interests
        user_interests = [
            'home decoration', 'healthy cooking', 'fitness and wellness', 'fashion trends',
            'technology gadgets', 'beauty and skincare', 'gardening', 'outdoor activities',
            'quality products', 'modern lifestyle', 'family care',
            'professional development', 'creative hobbies', 'travel and adventure'
        ]

        for i in range(50):
            user = self.models['User'](
                username=fake.user_name() + str(random.randint(100, 999)),
                email=fake.email(),
                password=hash_password('password123'),
                first_name=fake.first_name(),
                last_name=fake.last_name(),
                phone=fake.phone_number()[:15],
                date_of_birth=fake.date_of_birth(minimum_age=18, maximum_age=70),
                is_active=True,
                newsletter_subscribed=random.choice([True, False]),
                email_notifications=random.choice([True, False]),
                sms_notifications=random.choice([True, False]),
                bio=f"Passionate about {random.choice(user_interests)} and quality living.",
                created_at=random_datetime_between(
                    datetime.utcnow() - timedelta(days=365),
                    datetime.utcnow() - timedelta(days=30)
                ),
                last_login=random_datetime_between(
                    datetime.utcnow() - timedelta(days=30),
                    datetime.utcnow()
                )
            )

            self.db.session.add(user)
            self.created_data['users'].append(user)

        self.db.session.commit()
        print(f"✅ Created {len(self.created_data['users'])} users")

    def create_sellers(self):
        """Create seller accounts"""
        print("🏪 Creating sellers...")

        for seller_data in SUSTAINABLE_SELLERS:
            seller = self.models['Seller'](
                business_name=seller_data['business_name'],
                contact_person=seller_data['contact_person'],
                email=fake.company_email(),
                phone=fake.phone_number()[:15],
                business_type='company',
                category='sustainable_products',
                description=seller_data['description'],
                address=fake.address()[:200],
                gst_number=fake.bothify(text='##AAAAA####A#Z#'),
                pan_number=fake.bothify(text='AAAAA####A'),
                bank_account=fake.bothify(text='################'),
                ifsc_code=fake.bothify(text='AAAA0######'),
                bank_holder_name=seller_data['contact_person'],
                commission_rate=random.uniform(5.0, 15.0),
                is_verified=True,
                status='approved',
                total_earnings=0.0,
                pending_earnings=0.0,
                paid_earnings=0.0,
                created_at=random_datetime_between(
                    datetime.utcnow() - timedelta(days=300),
                    datetime.utcnow() - timedelta(days=60)
                )
            )

            self.db.session.add(seller)
            self.created_data['sellers'].append(seller)

        self.db.session.commit()
        print(f"✅ Created {len(self.created_data['sellers'])} sellers")

    def create_products(self):
        """Create sustainable products"""
        print("🌱 Creating sustainable products...")

        product_count = 0
        target_products = 120  # Create 120 products total

        for category_name, products_list in SUSTAINABLE_PRODUCTS.items():
            category = next((c for c in self.created_data['categories'] if c.name == category_name), None)
            if not category:
                continue

            category_info = SUSTAINABLE_CATEGORIES[category_name]

            # Create products for this category
            for product_name, product_description in products_list:
                if product_count >= target_products:
                    break

                # Select random seller
                seller = random.choice(self.created_data['sellers'])

                # Generate price within category range
                min_price, max_price = category_info['price_range']
                base_price = random.uniform(min_price, max_price)

                # Add some price variation
                price = round(base_price * random.uniform(0.8, 1.2), 2)

                # Generate sustainability score
                min_score, max_score = category_info['sustainability_score']
                sustainability_score = random.randint(min_score, max_score)

                # Create detailed product description
                keywords = category_info['keywords']
                detailed_description = f"{product_description}. "
                detailed_description += f"This product features {random.choice(keywords)} materials and supports sustainable practices. "
                detailed_description += f"Perfect for eco-conscious consumers looking for {random.choice(keywords)} alternatives. "
                detailed_description += f"Sustainability score: {sustainability_score}/10."

                # Generate stock quantity
                stock_quantity = random.randint(10, 200)

                product = self.models['Product'](
                    name=product_name,
                    description=detailed_description,
                    price=price,
                    image=f"https://images.unsplash.com/photo-1542601906990-b4d3fb778b09?w=400&h=400&fit=crop&crop=center&q=80&auto=format",
                    sku=f"ECO-{fake.bothify(text='####-####')}",
                    stock_quantity=stock_quantity,
                    low_stock_threshold=random.randint(5, 20),
                    seller_id=seller.id,
                    category=category_name,
                    brand=seller.business_name,
                    weight=random.uniform(0.1, 5.0),
                    dimensions=f"{random.uniform(5, 50):.1f}x{random.uniform(5, 50):.1f}x{random.uniform(2, 30):.1f}",
                    sustainability_score=sustainability_score,
                    material=random.choice(keywords),
                    care_instructions=f"Handle with care. {random.choice(['Machine washable', 'Hand wash only', 'Dry clean', 'Wipe clean', 'Air dry'])}.",
                    created_at=random_datetime_between(
                        datetime.utcnow() - timedelta(days=180),
                        datetime.utcnow() - timedelta(days=1)
                    ),
                    updated_at=datetime.utcnow()
                )

                self.db.session.add(product)
                self.created_data['products'].append(product)
                product_count += 1

        self.db.session.commit()
        print(f"✅ Created {len(self.created_data['products'])} sustainable products")

    def create_product_images(self):
        """Create product images"""
        print("📸 Creating product images...")

        # Sample sustainable product image URLs (placeholder URLs)
        image_base_urls = [
            "https://images.unsplash.com/photo-1542601906990-b4d3fb778b09",  # Eco products
            "https://images.unsplash.com/photo-1558618666-fcd25c85cd64",  # Sustainable items
            "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b",  # Green products
            "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136",  # Organic items
            "https://images.unsplash.com/photo-1584464491033-06628f3a6b7b",  # Natural products
            "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136",  # Bamboo products
            "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b",  # Eco-friendly
            "https://images.unsplash.com/photo-1542601906990-b4d3fb778b09"   # Sustainable
        ]

        for product in self.created_data['products']:
            # Create 2-4 images per product
            num_images = random.randint(2, 4)

            for i in range(num_images):
                image_url = f"{random.choice(image_base_urls)}?w=800&h=600&fit=crop&crop=center&q=80&auto=format"

                product_image = self.models['ProductImage'](
                    product_id=product.id,
                    image_url=image_url,
                    alt_text=f"{product.name} - Image {i+1}",
                    is_primary=(i == 0),
                    display_order=i + 1,
                    created_at=product.created_at
                )

                self.db.session.add(product_image)

        self.db.session.commit()
        print("✅ Created product images")

    def create_orders(self):
        """Create realistic order data"""
        print("🛒 Creating orders...")

        order_statuses = ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled']
        payment_statuses = ['pending', 'paid', 'failed', 'refunded']
        payment_methods = ['credit_card', 'debit_card', 'upi', 'net_banking', 'wallet', 'cod']

        # Create 200 orders
        for i in range(200):
            user = random.choice(self.created_data['users'])

            # Generate order date
            order_date = random_datetime_between(
                datetime.utcnow() - timedelta(days=120),
                datetime.utcnow() - timedelta(days=1)
            )

            # Select 1-5 products for this order
            order_products = random.sample(self.created_data['products'], random.randint(1, 5))

            # Calculate order totals
            subtotal = 0
            total_items = 0

            for product in order_products:
                quantity = random.randint(1, 3)
                subtotal += product.price * quantity
                total_items += quantity

            # Add shipping and tax
            shipping_cost = random.uniform(5, 25) if subtotal < 500 else 0  # Free shipping over 500
            tax_amount = subtotal * 0.18  # 18% GST
            total_amount = subtotal + shipping_cost + tax_amount

            order = self.models['Order'](
                user_id=user.id,
                order_number=f"ORD-{fake.bothify(text='####-####')}",
                status=random.choice(order_statuses),
                payment_status=random.choice(payment_statuses),
                payment_method=random.choice(payment_methods),
                subtotal=round(subtotal, 2),
                tax_amount=round(tax_amount, 2),
                shipping_amount=round(shipping_cost, 2),
                discount_amount=0.0,
                total_amount=round(total_amount, 2),
                shipping_address={
                    'name': f"{user.first_name} {user.last_name}",
                    'address_line_1': fake.street_address(),
                    'address_line_2': fake.secondary_address(),
                    'city': fake.city(),
                    'state': fake.state(),
                    'postal_code': fake.postcode(),
                    'country': 'India',
                    'phone': user.phone
                },
                billing_address={
                    'name': f"{user.first_name} {user.last_name}",
                    'address_line_1': fake.street_address(),
                    'city': fake.city(),
                    'state': fake.state(),
                    'postal_code': fake.postcode(),
                    'country': 'India'
                },
                tracking_number=f"TRK-{fake.bothify(text='############')}" if random.choice([True, False]) else None,
                estimated_delivery=(order_date + timedelta(days=random.randint(3, 10))).date(),
                created_at=order_date,
                updated_at=order_date
            )

            self.db.session.add(order)
            self.db.session.flush()  # Flush to get the order ID
            self.created_data['orders'].append(order)

            # Create order items
            for product in order_products:
                quantity = random.randint(1, 3)
                unit_price = product.price

                order_item = self.models['OrderItem'](
                    order_id=order.id,
                    product_id=product.id,
                    quantity=quantity,
                    unit_price=unit_price,
                    total_price=round(unit_price * quantity, 2),
                    product_name=product.name,
                    product_image=product.image,
                    created_at=order_date
                )

                self.db.session.add(order_item)

                # Update product stock
                product.stock_quantity = max(0, product.stock_quantity - quantity)

        self.db.session.commit()
        print(f"✅ Created {len(self.created_data['orders'])} orders with items")

    def create_product_reviews(self):
        """Create product reviews"""
        print("⭐ Creating product reviews...")

        review_templates = [
            "Amazing product! Really happy with the quality and fast shipping.",
            "Love this {product_name}! It's exactly what I was looking for.",
            "Great quality and excellent value. Would definitely recommend to other buyers.",
            "Perfect for my needs. The {product_name} exceeded my expectations.",
            "Excellent product. The quality is outstanding and very well made.",
            "Really impressed with this product. Great value for money.",
            "This {product_name} is exactly what I needed. Highly recommended!",
            "Outstanding quality and great design. Will definitely buy again.",
            "Perfect product. Great for anyone looking for quality items.",
            "Excellent choice for quality-conscious consumers. Very satisfied!"
        ]

        # Create 500+ reviews
        for _ in range(500):
            product = random.choice(self.created_data['products'])
            user = random.choice(self.created_data['users'])

            # Check if user already reviewed this product
            existing_review = any(
                r.product_id == product.id and r.user_id == user.id
                for r in self.created_data['reviews']
            )

            if existing_review:
                continue

            rating = random.choices([1, 2, 3, 4, 5], weights=[2, 3, 10, 35, 50])[0]  # Weighted towards higher ratings

            review_text = random.choice(review_templates).format(product_name=product.name.lower())

            # Add specific quality comments for high ratings
            if rating >= 4:
                quality_comments = [
                    " The packaging was very professional and secure.",
                    " Love the attention to detail in the design.",
                    " Great to support businesses that care about quality.",
                    " Perfect for my lifestyle and needs.",
                    " The product information was very helpful."
                ]
                review_text += random.choice(quality_comments)

            review_date = random_datetime_between(
                product.created_at + timedelta(days=1),
                datetime.utcnow()
            )

            review = self.models['ProductReview'](
                product_id=product.id,
                user_id=user.id,
                rating=rating,
                title=f"{'Great' if rating >= 4 else 'Good' if rating >= 3 else 'Okay'} sustainable product",
                comment=review_text,
                verified_purchase=random.choice([True, False]),
                helpful_count=random.randint(0, 20),
                created_at=review_date,
                updated_at=review_date
            )

            self.db.session.add(review)
            self.created_data['reviews'].append(review)

        self.db.session.commit()
        print(f"✅ Created {len(self.created_data['reviews'])} product reviews")

    def create_hashtags(self):
        """Create sustainability hashtags"""
        print("🏷️  Creating hashtags...")

        for tag in SUSTAINABILITY_HASHTAGS:
            hashtag = self.models['Hashtag'](
                tag=tag,
                usage_count=random.randint(5, 100),
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )

            self.db.session.add(hashtag)
            self.created_data['hashtags'].append(hashtag)

        self.db.session.commit()
        print(f"✅ Created {len(self.created_data['hashtags'])} hashtags")

    def create_community_posts(self):
        """Create community posts about sustainability"""
        print("💬 Creating community posts...")

        post_templates = [
            "Just got these amazing {product_type} and absolutely loving them! 😍",
            "Can't believe the quality of these {product_type}! Totally worth it! ⭐",
            "Sharing my latest find - {product_type}. Highly recommend! 👍",
            "These products have been game-changers for my daily routine! 🙌",
            "Love discovering new brands that focus on quality! 💝",
            "Small upgrades, big difference! These {product_type} are perfect. ✨",
            "Life hack: Invest in quality {product_type} for better results! 💡",
            "Excited to share my latest purchase! {product_type} 🛍️",
            "Upgrading my collection one quality item at a time! 📈",
            "These {product_type} prove that quality and style can go together! 💯"
        ]

        product_types = [
            'home essentials', 'fashion items', 'tech gadgets',
            'beauty products', 'kitchen tools', 'fitness gear',
            'food products', 'baby items', 'garden supplies',
            'electronic devices'
        ]

        # Create 150 community posts
        for _ in range(150):
            user = random.choice(self.created_data['users'])
            product_type = random.choice(product_types)

            content = random.choice(post_templates).format(product_type=product_type)

            # Add more detailed content for some posts
            if random.choice([True, False]):
                additional_content = [
                    " The quality is amazing and exceeded my expectations completely.",
                    " It's incredible how much difference good products can make in daily life.",
                    " Perfect for anyone looking for reliable and high-quality items.",
                    " The packaging was so well done and arrived in perfect condition!",
                    " Supporting companies that focus on quality and customer satisfaction feels great."
                ]
                content += random.choice(additional_content)

            post_date = random_datetime_between(
                datetime.utcnow() - timedelta(days=90),
                datetime.utcnow()
            )

            post = self.models['CommunityPost'](
                user_id=user.id,
                content=content,
                post_type=random.choice(['text', 'photo', 'review']),
                image_url=f"https://images.unsplash.com/photo-{random.randint(1500000000000, 1600000000000)}" if random.choice([True, False]) else None,
                likes_count=random.randint(0, 50),
                comments_count=random.randint(0, 15),
                shares_count=random.randint(0, 10),
                created_at=post_date,
                updated_at=post_date
            )

            self.db.session.add(post)
            self.db.session.flush()  # Flush to get the post ID
            self.created_data['posts'].append(post)

            # Add hashtags to posts
            post_hashtags = random.sample(self.created_data['hashtags'], random.randint(1, 4))
            for hashtag in post_hashtags:
                post_hashtag = self.models['PostHashtag'](
                    post_id=post.id,
                    hashtag_id=hashtag.id,
                    created_at=post_date
                )
                self.db.session.add(post_hashtag)

                # Update hashtag usage count
                hashtag.usage_count += 1

        self.db.session.commit()
        print(f"✅ Created {len(self.created_data['posts'])} community posts")

    def create_community_engagement(self):
        """Create likes and comments for community posts"""
        print("❤️  Creating community engagement...")

        comment_templates = [
            "This is amazing! Thanks for sharing! 🌱",
            "I've been looking for something like this! Where did you get it?",
            "Love this! Sustainability is so important 💚",
            "Great choice! I have the same one and love it!",
            "This is exactly what I need for my eco-friendly lifestyle!",
            "Thanks for the recommendation! Adding to my wishlist 🌿",
            "Sustainable living goals! 🌍",
            "This looks perfect! How long have you been using it?",
            "Love seeing more people make sustainable choices! ♻️",
            "Great post! More people need to see this!"
        ]

        # Create likes for posts
        for post in self.created_data['posts']:
            # Random number of likes (already set in post.likes_count)
            num_likes = min(post.likes_count, len(self.created_data['users']))
            liked_users = random.sample(self.created_data['users'], num_likes)

            for user in liked_users:
                like = self.models['PostLike'](
                    post_id=post.id,
                    user_id=user.id,
                    created_at=random_datetime_between(
                        post.created_at,
                        post.created_at + timedelta(days=7)
                    )
                )
                self.db.session.add(like)

        # Create comments for posts
        for post in self.created_data['posts']:
            # Random number of comments (already set in post.comments_count)
            for _ in range(post.comments_count):
                user = random.choice(self.created_data['users'])

                comment = self.models['PostComment'](
                    post_id=post.id,
                    user_id=user.id,
                    content=random.choice(comment_templates),
                    created_at=random_datetime_between(
                        post.created_at,
                        post.created_at + timedelta(days=10)
                    ),
                    updated_at=datetime.utcnow()
                )
                self.db.session.add(comment)

        self.db.session.commit()
        print("✅ Created community engagement (likes and comments)")

    def create_supporting_data(self):
        """Create supporting data like shipping zones, payment gateways, etc."""
        print("🔧 Creating supporting data...")

        # Create shipping zones
        shipping_zones_data = [
            {
                'name': 'India Domestic',
                'countries': ['IN'],
                'states': None,
                'postal_codes': None,
                'is_active': True
            },
            {
                'name': 'International',
                'countries': ['US', 'UK', 'CA', 'AU', 'DE', 'FR'],
                'states': None,
                'postal_codes': None,
                'is_active': True
            }
        ]

        for zone_data in shipping_zones_data:
            zone = self.models['ShippingZone'](
                name=zone_data['name'],
                countries=json.dumps(zone_data['countries']),
                states=json.dumps(zone_data['states']) if zone_data['states'] else None,
                postal_codes=json.dumps(zone_data['postal_codes']) if zone_data['postal_codes'] else None,
                is_active=zone_data['is_active']
            )
            self.db.session.add(zone)
            self.created_data['shipping_zones'].append(zone)

        # Flush to get zone IDs
        self.db.session.flush()

        # Create shipping methods
        shipping_methods_data = [
            {'name': 'Standard Shipping', 'description': 'Eco-friendly standard delivery', 'base_cost': 50.0, 'cost_per_kg': 10.0, 'min_delivery_days': 3, 'max_delivery_days': 7},
            {'name': 'Express Shipping', 'description': 'Fast delivery with carbon offset', 'base_cost': 100.0, 'cost_per_kg': 20.0, 'min_delivery_days': 1, 'max_delivery_days': 3},
            {'name': 'Free Shipping', 'description': 'Free sustainable shipping over ₹500', 'base_cost': 0.0, 'cost_per_kg': 0.0, 'min_delivery_days': 5, 'max_delivery_days': 10}
        ]

        for method_data in shipping_methods_data:
            for zone in self.created_data['shipping_zones']:
                method = self.models['ShippingMethod'](
                    zone_id=zone.id,
                    name=method_data['name'],
                    description=method_data['description'],
                    base_cost=method_data['base_cost'],
                    cost_per_kg=method_data['cost_per_kg'],
                    free_shipping_threshold=500.0 if method_data['name'] == 'Free Shipping' else None,
                    min_delivery_days=method_data['min_delivery_days'],
                    max_delivery_days=method_data['max_delivery_days'],
                    is_active=True
                )
                self.db.session.add(method)
                self.created_data['shipping_methods'].append(method)

        # Create coupons
        coupons_data = [
            {'code': 'SUSTAINABLE20', 'name': 'Sustainable Living 20% Off', 'discount_type': 'percentage', 'discount_value': 20.0, 'minimum_amount': 200.0},
            {'code': 'ECOFRIENDLY15', 'name': 'Eco-Friendly 15% Discount', 'discount_type': 'percentage', 'discount_value': 15.0, 'minimum_amount': 150.0},
            {'code': 'GREENLIVING50', 'name': 'Green Living ₹50 Off', 'discount_type': 'fixed', 'discount_value': 50.0, 'minimum_amount': 300.0},
            {'code': 'ZEROWASTE25', 'name': 'Zero Waste 25% Off', 'discount_type': 'percentage', 'discount_value': 25.0, 'minimum_amount': 500.0},
            {'code': 'NEWUSER10', 'name': 'New User 10% Off', 'discount_type': 'percentage', 'discount_value': 10.0, 'minimum_amount': 100.0}
        ]

        for coupon_data in coupons_data:
            coupon = self.models['Coupon'](
                code=coupon_data['code'],
                name=coupon_data['name'],
                description=f"Get {coupon_data['discount_value']}{'%' if coupon_data['discount_type'] == 'percentage' else '₹'} off on sustainable products",
                discount_type=coupon_data['discount_type'],
                discount_value=coupon_data['discount_value'],
                minimum_amount=coupon_data['minimum_amount'],
                maximum_discount=100.0 if coupon_data['discount_type'] == 'percentage' else None,
                usage_limit=1000,
                usage_limit_per_user=5,
                is_active=True,
                valid_from=datetime.utcnow() - timedelta(days=30),
                valid_until=datetime.utcnow() + timedelta(days=90),
                created_at=datetime.utcnow()
            )
            self.db.session.add(coupon)
            self.created_data['coupons'].append(coupon)

        # Create tax rates
        tax_rate = self.models['TaxRate'](
            name='GST India',
            rate=0.18,  # 18% GST
            country='India',
            state=None,
            is_active=True,
            created_at=datetime.utcnow()
        )
        self.db.session.add(tax_rate)
        self.created_data['tax_rates'].append(tax_rate)

        # Create payment gateways
        payment_gateways_data = [
            {'name': 'razorpay', 'display_name': 'Razorpay', 'is_active': True},
            {'name': 'stripe', 'display_name': 'Stripe', 'is_active': True},
            {'name': 'paypal', 'display_name': 'PayPal', 'is_active': True}
        ]

        for gateway_data in payment_gateways_data:
            gateway = self.models['PaymentGateway'](
                name=gateway_data['name'],
                display_name=gateway_data['display_name'],
                is_active=gateway_data['is_active'],
                supported_currencies=['INR', 'USD'],
                supported_payment_methods=['card', 'wallet', 'bank_transfer'],
                processing_fee_percentage=2.5,
                min_amount=1.0,
                max_amount=100000.0
            )
            self.db.session.add(gateway)
            self.created_data['payment_gateways'].append(gateway)

        # Create OAuth providers
        oauth_providers_data = [
            {
                'name': 'google',
                'display_name': 'Google',
                'client_id': 'google-client-id',
                'client_secret': 'google-client-secret',
                'authorization_url': 'https://accounts.google.com/o/oauth2/auth',
                'token_url': 'https://oauth2.googleapis.com/token',
                'user_info_url': 'https://www.googleapis.com/oauth2/v2/userinfo',
                'scope': 'openid email profile',
                'button_color': '#4285f4'
            },
            {
                'name': 'facebook',
                'display_name': 'Facebook',
                'client_id': 'facebook-client-id',
                'client_secret': 'facebook-client-secret',
                'authorization_url': 'https://www.facebook.com/v18.0/dialog/oauth',
                'token_url': 'https://graph.facebook.com/v18.0/oauth/access_token',
                'user_info_url': 'https://graph.facebook.com/me',
                'scope': 'email public_profile',
                'button_color': '#1877f2'
            }
        ]

        for provider_data in oauth_providers_data:
            provider = self.models['OAuthProvider'](
                name=provider_data['name'],
                display_name=provider_data['display_name'],
                client_id=provider_data['client_id'],
                client_secret=provider_data['client_secret'],
                authorization_url=provider_data['authorization_url'],
                token_url=provider_data['token_url'],
                user_info_url=provider_data['user_info_url'],
                scope=provider_data['scope'],
                button_color=provider_data['button_color'],
                is_active=True,
                created_at=datetime.utcnow()
            )
            self.db.session.add(provider)
            self.created_data['oauth_providers'].append(provider)

        # Create shipping carriers
        carriers_data = [
            {'name': 'Shiprocket', 'code': 'SHIPROCKET', 'is_active': True},
            {'name': 'Delhivery', 'code': 'DELHIVERY', 'is_active': True},
            {'name': 'Ekart', 'code': 'EKART', 'is_active': True}
        ]

        for carrier_data in carriers_data:
            carrier = self.models['ShippingCarrier'](
                name=carrier_data['name'],
                code=carrier_data['code'],
                is_active=carrier_data['is_active'],
                supported_services=['standard', 'express'],
                rate_calculation_method='api',
                max_weight_kg=50.0,
                max_dimensions_cm={'length': 100, 'width': 100, 'height': 100},
                domestic_coverage=['IN'],
                international_coverage=['US', 'UK', 'CA', 'AU'],
                created_at=datetime.utcnow()
            )
            self.db.session.add(carrier)
            self.created_data['shipping_carriers'].append(carrier)

        self.db.session.commit()
        print("✅ Created supporting data (shipping, payments, etc.)")

    def run_seeding(self):
        """Run the complete database seeding process"""
        print("🌱 Starting Allora Sustainable E-commerce Database Seeding")
        print("=" * 60)

        try:
            # Initialize models
            if not self.initialize_models():
                print("❌ Failed to initialize models. Exiting.")
                return False

            # Clear existing data
            self.clear_existing_data()

            # Create data in dependency order
            self.create_categories()
            self.create_admin_users()
            self.create_users()
            self.create_sellers()
            self.create_products()
            self.create_product_images()
            self.create_orders()
            self.create_product_reviews()
            self.create_hashtags()
            self.create_community_posts()
            self.create_community_engagement()
            self.create_supporting_data()

            print("\n" + "=" * 60)
            print("🎉 Database seeding completed successfully!")
            print(f"📊 Created:")
            print(f"   • {len(self.created_data['categories'])} Categories")
            print(f"   • {len(self.created_data['admin_users'])} Admin Users")
            print(f"   • {len(self.created_data['users'])} Users")
            print(f"   • {len(self.created_data['sellers'])} Sellers")
            print(f"   • {len(self.created_data['products'])} Products")
            print(f"   • {len(self.created_data['orders'])} Orders")
            print(f"   • {len(self.created_data['reviews'])} Reviews")
            print(f"   • {len(self.created_data['posts'])} Community Posts")
            print(f"   • {len(self.created_data['hashtags'])} Hashtags")
            print(f"   • {len(self.created_data['coupons'])} Coupons")
            print(f"   • Supporting data (shipping, payments, etc.)")
            print("\n🌍 Your sustainable e-commerce platform is ready!")

            return True

        except Exception as e:
            print(f"\n❌ Error during seeding: {e}")
            self.db.session.rollback()
            return False

def main():
    """Main function to run the seeding script"""
    try:
        # Import Flask app and create application context
        from app import app

        with app.app_context():
            seeder = DatabaseSeeder()
            success = seeder.run_seeding()

            if success:
                print("\n✅ Seeding completed successfully!")
                return 0
            else:
                print("\n❌ Seeding failed!")
                return 1

    except Exception as e:
        print(f"❌ Failed to run seeding script: {e}")
        return 1

if __name__ == "__main__":
    exit(main())